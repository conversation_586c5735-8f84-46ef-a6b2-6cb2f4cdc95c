import request from '@/utils/request';

// 账户信息联想查询
export function suggestAcctInfo(keyword) {
  return request({
    url: '/api/admin/xzp/suggestAcctInfo',
    method: 'get',
    params: {
      keyword: keyword
    }
  });
}

// 账户信息分页查询
export function queryAcctInfoList(data) {
  return request({
    url: '/api/admin/xzp/queryAcctInfoList',
    method: 'post',
    params: {
      pageNum: data.current || 1,
      pageSize: data.size || 10
    },
    data: data
  });
}

// 新增账户信息
export function addAcctInfo(data) {
  return request({
    url: '/api/admin/xzp/addAcctInfo',
    method: 'post',
    data: data
  });
}

// 修改账户信息
export function updateAcctInfo(data) {
  return request({
    url: '/api/admin/xzp/updateAcctInfo',
    method: 'post',
    data: data
  });
}

// 删除账户信息
export function deleteAcctInfo(cpabAccId, acctNm) {
  return request({
    url: '/api/admin/xzp/deleteAcctInfo',
    method: 'post',
    params: {
      cpabAccId: cpabAccId,
      acctNm: acctNm
    }
  });
}

// 查看账户信息详情
export function getAcctInfo(id) {
  return request({
    url: `/api/admin/xzp/getAcctInfo/${id}`,
    method: 'get'
  });
}