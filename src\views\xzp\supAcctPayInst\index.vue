<!--监管账户支付指令控制表-->
<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" ref="search" class="search-form" :rules="rules" label-width="120px" size="small">
      <el-row :gutter="24">
        <el-col :span="11">
          <el-form-item label="业务代码" prop="opeCd"  required: true>
            <el-autocomplete
              v-model="search.opeCd"
              :fetch-suggestions="queryOpeCd"
              placeholder="请输入业务代码"
              @select="handleOpeCdSelect"
              @clear="handlerOpeCdClear"
              :trigger-on-focus="true"
              style="width: 280px;"
              clearable
            >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value +'-'+ item.item.opeNm }}</div>
              </div>
            </template>
            </el-autocomplete>
            <span v-if="search.opeNm" class="tip-text">{{ search.opeNm }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="13">
          <el-form-item label="委托单位代码" prop="merchId"  required: true>
            <el-autocomplete
              v-model="search.merchId"
              :fetch-suggestions="queryMerchId"
              placeholder="请输入委托单位代码"
              @select="handleMerchIdSelect"
              @clear="handlerMerchIdClear"
              :trigger-on-focus="true"
              style="width: 280px;"
              clearable
            >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.item.prdtNm}}</div>
              </div>
            </template>
            </el-autocomplete>
            <span v-if="search.merchNm" class="tip-text">{{ search.merchNm }}</span>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter="24" >
        <el-col :span="11">
          <el-form-item label="指令类型" prop="transType"  required: true>
            <el-select v-model="search.transType" clearable placeholder="请选择指令类型" style="width: 280px;">
              <el-option
                v-for="dict in dict.type.xzp_record_sta"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="13">
            <el-form-item label="指令流水号">
              <el-input v-model="search.tranSq" placeholder="请输入指令流水号" class="custom-input" clearable style="width: 280px;" />
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" >
          <el-col :span="11">
            <el-form-item label="指令起止日期" >
              <el-date-picker
                v-model="search.startDt"
                type="date"
                placeholder="选择起始日期"
                value-format="yyyyMMdd"
                class="custom-input"
                clearable
                @change="validateEndTime"
                style="width: 135px;"
              />
              -
              <el-date-picker
                v-model="search.endDt"
                type="date"
                placeholder="选择终止日期"
                value-format="yyyyMMdd"
                class="custom-input"
                clearable
                @change="validateEndTime"
                style="width: 135px;"
              />
            </el-form-item>
          </el-col>
      </el-row>


      <el-row>  
        <el-col :span="24" class="search-btns">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    </el-card>
      
      
      <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      :search.sync="search"
      :before-open="beforeOpen"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      
      v-model="formParent"
      @selection-change="handleSelectionChange"
    >
    
      <!-- 自定义操作按钮 -->
       <template slot-scope="{row,size,type}" slot="menu">
        <el-button :size="size" :type="type" @click="handleCommand(row)" icon="el-icon-info">
          查看
        </el-button>
      </template>

    </yo-table>
    
  </div>
</template>

<script>
import { supAcctPayInstList} from '@/api/supAcctPayInst';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import supAcctPayInstTableOption from './infoData/supAcctPayInstTableOption.js';

export default {
  name: 'supAcctPayInst',
  dicts: ['xzp_record_type','xzp_record_sta','xzp_tran_fg'],
  data() {
    return {
      loading: false,
      formParent: {},
      search: {
        opeCd: '',
        opeNm:'',
        merchId:'',
        merchNm:'',
        transType:'',
        tranSq:'',
        startDt:'',
        endDt:''
      },
      page: {
        pageSize: 10,
        pageNum: 1,
      },
      data: [],
      option: supAcctPayInstTableOption,
      multiple: true,
      // 自动完成相关数据
      opeCdOptions: [], // 业务代码选项
      merchIdOptions: [], // 委托单位代码选项
      opeCdLoading: false,
      merchIdLoading: false,
      rules: {
        opeCd: [
          { required: true, message: '请选择业务代码', trigger: 'change' }
        ],
        merchId: [
          { required: true, message: '请选择委托方单位代码', trigger: 'change' }
        ],
        transType: [
          { required: true, message: '请选择委托方单位代码', trigger: 'change' }
        ]
      },
    };
  },
  created() {
    this.page.currentPage = 1;
    this.getList();
    // 更新字典数据
    this.updateDictData(this.option.column, 'recordType', this.dict.type.xzp_record_type);
    this.updateDictData(this.option.column, 'transType', this.dict.type.xzp_record_sta);
    this.updateDictData(this.option.column, 'tranFg', this.dict.type.xzp_tran_fg);
  },
  methods: {
    // 更新字典数据
    updateDictData(option, key, data) {
      // 更新表格配置中的状态选项
      const column = this.findObject(option, key);
      if (column) {
        column.dicData = data;
      }
    },
    handleCommand(row) {
      this.$refs.crud.rowView(row);
    },
    // 查找对象
    findObject(array, prop) {
      return array.find(item => item.prop === prop);
    },
    onLoad() {
      this.getList();
    },
    beforeOpen(done, type) {
      done();
    },
    searchChange(params, done) {
      this.handleQuery();
      setTimeout(() => {
        done();
      }, 1000);
    },
    refresh() {
      this.handleQuery();
    },
    async getList(query) {
      const params = { ...this.search, ...this.page };
      this.loading = true;
      try {
        const { code, data } = await supAcctPayInstList(params);
        if (code === '0') {
          this.data = data.list;
          this.page.total = data.total;
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.$refs['search'].validate(valid => {
        if (valid) {
          this.page.currentPage = 1;
          this.getList();
        } else {
          //this.$message.error('请正确填写查询条件');
          return false;
        }
      });
    },
    resetQuery() {
      this.search = {
        recordType: '',
        merchDt: '',
        recordSta: ''
      };
      this.opeCdOptions = [];
      this.merchIdOptions = [];
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.multiple = !(selection.length > 0);
    },
    
    
    // 选择业务代码
    handleOpeCdSelect(item) {
      this.search.opeCd = item.value;
      this.search.opeNm = item.item.opeNm;
      // 清空委托单位代码，触发重新查询
      if (this.search.merchId) {
        this.search.merchId = '';
        this.search.merchNm = '';
      }
    },
    // 清除方法
    handlerOpeCdClear(){
      this.search.opeCd = ''
      this.search.opeNm = ''
    },
    handlerMerchIdClear(){
      this.search.merchId = ''
      this.search.merchNm = ''
    },
    // 选择委托单位代码
    handleMerchIdSelect(item) {
      this.search.merchId = item.value;
      this.search.merchNm = item.item.prdtNm;
      // 如果没有业务代码，可以根据选择的委托单位代码查询相关的业务代码
      if (!this.search.opeCd) {
        // 可以在这里添加查询逻辑

      }
    },
    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.search.opeCd
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },
    /** 验证结束日期 */
    validateEndTime() {
      if (this.search.startDt && this.search.endDt) {
        if (Number(this.search.endDt) < Number(this.search.startDt)) {
          this.$message.warning('终止日期不能小于起始日期')
          this.search.endDt = ''
        }
      }
    },
  }
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}
.search-btns {
  text-align: right;
  padding-right: 50px;
}
.tip-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}
.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 