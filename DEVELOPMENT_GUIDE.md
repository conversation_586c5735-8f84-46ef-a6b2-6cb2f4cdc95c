# YOAF-Web 开发指南

## 开发环境搭建

### 1. 环境要求
- Node.js >= 8.9
- npm >= 3.0.0
- Git
- 现代浏览器 (Chrome, Firefox, Safari, Edge)

### 2. 项目克隆
```bash
git clone http://192.168.11.10/root/yoaf-web-0.0.0.1.git
cd yoaf-web-0.0.0.1
```

### 3. 依赖安装
```bash
npm install
```

### 4. 启动开发服务器
```bash
npm run dev
```

## 开发规范

### 代码风格
- 使用 2 个空格缩进
- 使用单引号
- 行末不加分号
- 最大行长度 100 字符

### 命名规范
- **组件名**: PascalCase (如: `UserManagement`)
- **文件名**: kebab-case (如: `user-management.vue`)
- **变量名**: camelCase (如: `userName`)
- **常量名**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)

### 目录结构规范
```
src/views/模块名/
├── index.vue              # 主页面
├── components/            # 模块专用组件
│   ├── UserForm.vue
│   └── UserTable.vue
└── infoData/             # 配置文件
    └── userOption.js
```

## 组件开发

### 1. 页面组件模板
```vue
<template>
  <div class="page-container">
    <yo-table
      ref="crud"
      :option="option"
      :data="data"
      v-model="form"
      :table-loading="loading"
      :page.sync="page"
      :search.sync="query"
      @on-load="onLoad"
      @search-change="searchChange"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @row-del="rowDel"
    />
  </div>
</template>

<script>
import { getList, add, update, remove } from '@/api/module'
import option from './infoData/moduleOption'

export default {
  name: 'ModuleName',
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: option,
      data: []
    }
  },
  methods: {
    onLoad(page, params = {}) {
      // 数据加载逻辑
    },
    searchChange(params, done) {
      // 搜索逻辑
    },
    rowSave(row, loading, done) {
      // 新增逻辑
    },
    rowUpdate(row, index, loading, done) {
      // 更新逻辑
    },
    rowDel(row, index) {
      // 删除逻辑
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}
</style>
```

### 2. API 接口规范
```javascript
// api/module.js
import request from '@/utils/request'

// 查询列表
export function getList(data) {
  return request({
    url: '/api/module/list',
    method: 'post',
    data: data
  })
}

// 新增
export function add(data) {
  return request({
    url: '/api/module/add',
    method: 'post',
    data: data
  })
}

// 更新
export function update(data) {
  return request({
    url: '/api/module/update',
    method: 'post',
    data: data
  })
}

// 删除
export function remove(id) {
  return request({
    url: `/api/module/delete/${id}`,
    method: 'delete'
  })
}
```

### 3. 表格配置规范
```javascript
// infoData/moduleOption.js
export default {
  index: true,
  indexLabel: '序号',
  selection: true,
  align: 'center',
  menuAlign: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  viewBtn: true,
  searchBtn: true,
  refreshBtn: true,
  columnBtn: true,
  labelWidth: 120,
  column: [
    {
      label: '字段名',
      prop: 'fieldName',
      width: 120,
      search: true,
      rules: [{
        required: true,
        message: '请输入字段名',
        trigger: 'blur'
      }]
    }
  ]
}
```

## 常用工具函数

### 1. 日期格式化
```javascript
import { formatDate } from '@/utils/index'

// 格式化日期
const formattedDate = formatDate(new Date(), 'YYYY-MM-DD')
```

### 2. 权限检查
```javascript
// 在模板中使用
<el-button v-hasPermi="['admin:user:add']">新增</el-button>

// 在脚本中使用
import { checkPermi } from '@/utils/permission'

if (checkPermi(['admin:user:add'])) {
  // 有权限的操作
}
```

### 3. 消息提示
```javascript
// 成功提示
this.$message.success('操作成功')

// 错误提示
this.$message.error('操作失败')

// 确认对话框
this.$confirm('确定要删除吗？', '提示', {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning'
}).then(() => {
  // 确认操作
})
```

## 调试技巧

### 1. Vue DevTools
安装 Vue DevTools 浏览器扩展，用于调试 Vue 组件状态

### 2. 网络请求调试
在浏览器开发者工具的 Network 面板查看 API 请求

### 3. 控制台调试
```javascript
// 在组件中添加调试信息
console.log('调试信息:', this.data)

// 使用 debugger 断点
debugger
```

## 常见问题

### 1. 跨域问题
在 `vue.config.js` 中配置代理：
```javascript
proxy: {
  '/api': {
    target: 'http://localhost:8080',
    changeOrigin: true
  }
}
```

### 2. 路由权限
在路由配置中添加权限标识：
```javascript
{
  path: '/user',
  component: User,
  meta: {
    roles: ['admin', 'user']
  }
}
```

### 3. 组件通信
- 父子组件: props / $emit
- 兄弟组件: EventBus 或 Vuex
- 跨级组件: provide / inject 或 Vuex

## 性能优化

### 1. 路由懒加载
```javascript
const User = () => import('@/views/user/index')
```

### 2. 组件懒加载
```javascript
components: {
  UserForm: () => import('./components/UserForm')
}
```

### 3. 图片优化
- 使用 WebP 格式
- 压缩图片大小
- 使用 CDN

## 测试

### 1. 单元测试
```bash
npm run test:unit
```

### 2. 端到端测试
```bash
npm run test:e2e
```

## 构建部署

### 1. 开发环境构建
```bash
npm run build:dev
```

### 2. 生产环境构建
```bash
npm run build
```

### 3. 预览构建结果
```bash
npm run preview
```

---

更多详细信息请参考 [项目文档](./PROJECT_DOCUMENTATION.md)
